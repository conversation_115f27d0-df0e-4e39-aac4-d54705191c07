import { useState, useRef } from 'react';
import StepChannels from './components/StepChannels';
import StepKPISetup from '../Personalization/components/StepKPISetup/StepKPISetup';
import StepReview from './components/StepReview';
import { Box, Divider, Flex, Button } from '@chakra-ui/react';
import ProgressBar from './components/ProgressBar';
import { PersonalizationFormData } from './utils/types';

const steps = ['Channels', 'KPI Setup', 'Review'];

const Personalization = () => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<PersonalizationFormData>({
    selectedChannels: [],
    channelConfigs: {},
  });
  const reviewRef = useRef<{ handleSave: () => void }>(null);

  const nextStep = () => {
    if (step === steps.length) {
      reviewRef.current?.handleSave();
    } else {
      if (step === 1 && formData.selectedChannels.length === 0) return;
      setStep((s) => Math.min(s + 1, steps.length));
    }
  };

  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const isNextDisabled = () => {
    switch (step) {
      case 1:
        return formData.selectedChannels.length === 0;
      case 2:
        return formData.selectedChannels.some(
          (ch) =>
            !formData.channelConfigs[ch]?.primaryKPI ||
            formData.channelConfigs[ch]?.benchmarks.length === 0
        );
      default:
        return false;
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return <StepChannels data={formData} setData={setFormData} />;
      case 2:
        return <StepKPISetup data={formData} setData={setFormData} />;
      case 3:
        return <StepReview ref={reviewRef} data={formData} />;
      default:
        return null;
    }
  };

  return (
    <Flex direction="column" minH="100vh" w="100%">
      {/* Header + Progress */}
      <Box p={8} pb={0}>
        <Box mb={4} fontWeight="medium" fontFamily="Poppins, sans-serif" fontSize="xl">
          Customize Your AI CMO
        </Box>
        <ProgressBar steps={steps} currentStep={step} />
        <Divider mt={4} mb={6} borderColor="#D5D5D5" />
      </Box>

      {/* Scrollable content area */}
      <Box
        flex="1"
        overflowY="auto"
        px={8}
        pb={8}
        css={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#B0B0B0 #F0F0F0',
        }}
      >
        {renderStep()}
      </Box>

      {/* Sticky footer */}
      <Box
        position="sticky"
        bottom="0"
        bg="white"
        borderTop="1px solid"
        borderColor="gray.200"
        p={4}
      >
        <Flex justify="space-between" align="center">
          <Button variant="outline" onClick={prevStep}>
            Back
          </Button>
          <Flex gap={3}>
            <Button variant="ghost">Cancel</Button>
            <Button
              colorScheme="purple"
              onClick={nextStep}
              isDisabled={isNextDisabled()}
              opacity={isNextDisabled() ? 0.6 : 1}
              cursor={isNextDisabled() ? 'not-allowed' : 'pointer'}
            >
              {step === steps.length ? 'Save' : 'Next'}
            </Button>
          </Flex>
        </Flex>
      </Box>
    </Flex>
  );
};

export default Personalization;
